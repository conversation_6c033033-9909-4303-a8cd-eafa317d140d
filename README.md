# 本地图片浏览器

这是一个使用 Python Flask 构建的简单 Web 应用，用于浏览本地文件夹中的图片。

## 功能

*   浏览指定目录下的图片和子目录。
*   支持子目录导航。
*   图片和文件夹分页显示。
*   响应式布局，适配不同设备。

## 安装与运行

1.  **克隆或下载项目**

2.  **安装依赖**

    在项目根目录下打开终端，运行以下命令安装所需的 Python 包：
    ```bash
    pip install -r requirements.txt
    ```

3.  **配置图片目录**

    打开 `app.py` 文件，修改 `IMAGE_ROOT` 变量为您想要浏览的图片文件夹的绝对路径。
    ```python
    # app.py
    IMAGE_ROOT = '在此处填入你的图片文件夹路径' 
    ```
    例如: `C:/Users/<USER>/Pictures` 或 `/home/<USER>/images`。

4.  **运行应用**

    执行以下命令来启动 Flask 开发服务器：
    ```bash
    python app.py
    ```

5.  **访问应用**

    在浏览器中打开 `http://127.0.0.1:5000` 即可开始浏览图片。
