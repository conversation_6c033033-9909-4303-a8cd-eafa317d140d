# 🖼️ Flask图片浏览器 - 局域网版

一个功能强大的Flask图片浏览器应用，专为局域网访问优化，支持多设备同时浏览本地图片文件。

## ✨ 主要功能

### 🌐 局域网访问
- **自动IP检测**: 启动时自动显示局域网访问地址
- **多设备支持**: 手机、平板、电脑都可以访问
- **零配置**: 无需复杂网络设置

### 🎨 现代化界面
- **响应式设计**: 适配各种屏幕尺寸
- **网格/列表视图**: 两种浏览模式自由切换
- **图片预览**: 点击图片即可预览，无需下载
- **面包屑导航**: 清晰的目录层级显示

### 🔍 智能功能
- **实时搜索**: 快速查找文件和文件夹
- **分页浏览**: 大量图片也能流畅浏览
- **文件信息**: 显示文件大小和文件夹项目数
- **懒加载**: 图片按需加载，提升性能

### 🛡️ 安全特性
- **路径验证**: 防止目录遍历攻击
- **访问控制**: 严格的文件访问权限
- **错误处理**: 友好的错误页面
- **安全日志**: 记录所有安全事件

### ⚡ 性能优化
- **智能缓存**: 文件和目录信息缓存
- **HTTP缓存**: 浏览器缓存优化
- **多线程**: 支持并发访问
- **压缩传输**: 减少网络传输量

## 安装与运行

1.  **克隆或下载项目**

2.  **安装依赖**

    在项目根目录下打开终端，运行以下命令安装所需的 Python 包：
    ```bash
    pip install -r requirements.txt
    ```

3.  **配置图片目录**

    打开 `app.py` 文件，修改 `IMAGE_ROOT` 变量为您想要浏览的图片文件夹的绝对路径。
    ```python
    # app.py
    IMAGE_ROOT = '在此处填入你的图片文件夹路径' 
    ```
    例如: `C:/Users/<USER>/Pictures` 或 `/home/<USER>/images`。

4.  **运行应用**

    执行以下命令来启动 Flask 开发服务器：
    ```bash
    python app.py
    ```

5.  **访问应用**

    在浏览器中打开 `http://127.0.0.1:5000` 即可开始浏览图片。
