import os
import logging
import mimetypes
import time
import hashlib
from urllib.parse import unquote
from functools import lru_cache
from flask import Flask, render_template, send_from_directory, request, abort, jsonify, make_response
from werkzeug.exceptions import RequestEntityTooLarge, NotFound, Forbidden
from config import Config
from logging_config import setup_logging, log_error, log_security_event

# 缓存装饰器
@lru_cache(maxsize=1000)
def get_file_info_cached(file_path):
    """缓存文件信息"""
    try:
        stat = os.stat(file_path)
        return {
            'size': stat.st_size,
            'mtime': stat.st_mtime,
            'exists': True
        }
    except (OSError, IOError):
        return {'exists': False}

@lru_cache(maxsize=500)
def get_directory_items_cached(directory_path, supported_extensions_str, mtime):
    """缓存目录内容（基于修改时间）"""
    supported_extensions = eval(supported_extensions_str)  # 将字符串转回元组
    return get_directory_items_uncached(directory_path, supported_extensions)

def create_app(config_class=Config):
    """应用工厂函数"""
    app = Flask(__name__)
    app.config.from_object(config_class)

    # 配置日志
    app_logger = setup_logging(app)

    # 安全配置
    app.config['MAX_CONTENT_LENGTH'] = config_class.MAX_CONTENT_LENGTH

    # 性能配置
    app.config['SEND_FILE_MAX_AGE_DEFAULT'] = config_class.SEND_FILE_MAX_AGE_DEFAULT

    # 错误处理
    @app.errorhandler(404)
    def not_found_error(error):
        log_error("404", f"页面未找到: {request.url}", {
            'ip': request.remote_addr,
            'user_agent': request.headers.get('User-Agent'),
            'referer': request.headers.get('Referer')
        })
        return render_template('error.html',
                             error_code=404,
                             error_message="页面或文件未找到"), 404

    @app.errorhandler(403)
    def forbidden_error(error):
        log_security_event("访问被拒绝", f"路径: {request.url}", request.remote_addr)
        return render_template('error.html',
                             error_code=403,
                             error_message="访问被拒绝"), 403

    @app.errorhandler(500)
    def internal_error(error):
        log_error("500", f"服务器内部错误: {str(error)}", {
            'ip': request.remote_addr,
            'url': request.url,
            'method': request.method
        })
        return render_template('error.html',
                             error_code=500,
                             error_message="服务器内部错误"), 500

    @app.errorhandler(RequestEntityTooLarge)
    def file_too_large(error):
        log_error("413", f"文件过大: {request.url}", {
            'ip': request.remote_addr,
            'content_length': request.headers.get('Content-Length')
        })
        return render_template('error.html',
                             error_code=413,
                             error_message="文件过大"), 413
    
    def validate_path(path):
        """验证路径安全性"""
        try:
            # 解码URL编码的路径
            decoded_path = unquote(path)

            # 规范化路径
            normalized_path = os.path.normpath(decoded_path)

            # 检查是否包含危险字符
            dangerous_chars = ['..', '~', '$', '&', '|', ';', '`']
            if any(char in normalized_path for char in dangerous_chars):
                log_security_event("路径遍历攻击", f"危险路径: {path}", request.remote_addr)
                return False

            # 构建完整路径
            full_path = os.path.join(app.config['IMAGE_ROOT'], normalized_path)

            # 确保路径在允许的根目录内
            try:
                common_path = os.path.commonpath([app.config['IMAGE_ROOT'], full_path])
                if common_path != app.config['IMAGE_ROOT']:
                    log_security_event("路径遍历攻击", f"越界路径: {path}", request.remote_addr)
                    return False
            except ValueError:
                log_security_event("路径验证错误", f"无效路径: {path}", request.remote_addr)
                return False

            return True, normalized_path, full_path
        except Exception as e:
            log_error("路径验证异常", str(e), {'path': path, 'ip': request.remote_addr})
            return False

    @app.route('/')
    def index():
        """首页，显示根目录下的内容"""
        return gallery('.')

    @app.route('/<path:subpath>')
    def gallery(subpath):
        """显示子目录下的内容"""
        # 验证路径
        path_validation = validate_path(subpath)
        if not path_validation or path_validation is False:
            abort(403)

        _, normalized_path, current_path = path_validation

        # 检查目录是否存在
        if not os.path.exists(current_path):
            abort(404)

        if not os.path.isdir(current_path):
            abort(404)

        try:
            page = request.args.get('page', 1, type=int)
            if page < 1:
                page = 1

            # 获取目录修改时间用于缓存
            try:
                dir_mtime = os.path.getmtime(current_path)
            except OSError:
                dir_mtime = time.time()

            # 使用缓存获取目录项目
            all_items = get_directory_items_cached(
                current_path,
                str(app.config['SUPPORTED_EXTENSIONS']),
                dir_mtime
            )

            # 分页处理
            paginated_items, total_pages = paginate_items(all_items, page, app.config['PER_PAGE'])

            # 创建响应并添加缓存头
            response = make_response(render_template('index.html',
                                                   items=paginated_items,
                                                   current_dir=normalized_path,
                                                   page=page,
                                                   total_pages=total_pages,
                                                   total_items=len(all_items)))

            # 添加缓存控制头
            response.headers['Cache-Control'] = 'public, max-age=300'  # 5分钟缓存
            response.headers['ETag'] = hashlib.md5(f"{current_path}-{dir_mtime}-{page}".encode()).hexdigest()

            return response

        except Exception as e:
            logging.error(f"Error in gallery route: {e}")
            abort(500)

    @app.route('/images/<path:filename>')
    def get_image(filename):
        """提供图片文件"""
        # 验证路径
        path_validation = validate_path(filename)
        if not path_validation or path_validation is False:
            abort(403)

        _, normalized_filename, file_path = path_validation

        # 检查文件是否存在
        if not os.path.exists(file_path):
            abort(404)

        if not os.path.isfile(file_path):
            abort(404)

        # 检查文件扩展名
        file_ext = os.path.splitext(normalized_filename)[1].lower()
        if file_ext not in app.config['SUPPORTED_EXTENSIONS']:
            abort(403)

        try:
            # 获取文件信息用于缓存
            file_info = get_file_info_cached(file_path)
            if not file_info['exists']:
                abort(404)

            # 检查If-None-Match头（ETag缓存）
            etag = hashlib.md5(f"{file_path}-{file_info['mtime']}".encode()).hexdigest()
            if request.headers.get('If-None-Match') == etag:
                return '', 304  # Not Modified

            # 设置适当的MIME类型
            mimetype = mimetypes.guess_type(file_path)[0] or 'application/octet-stream'

            response = make_response(send_from_directory(
                app.config['IMAGE_ROOT'],
                normalized_filename,
                mimetype=mimetype,
                as_attachment=False,
                cache_timeout=app.config['SEND_FILE_MAX_AGE_DEFAULT']
            ))

            # 添加缓存头
            response.headers['ETag'] = etag
            response.headers['Cache-Control'] = f'public, max-age={app.config["SEND_FILE_MAX_AGE_DEFAULT"]}'
            response.headers['Last-Modified'] = time.strftime('%a, %d %b %Y %H:%M:%S GMT',
                                                            time.gmtime(file_info['mtime']))

            return response

        except Exception as e:
            logging.error(f"Error serving image {filename}: {e}")
            abort(500)

    # API端点用于获取目录信息
    @app.route('/api/directory/<path:subpath>')
    def api_directory(subpath):
        """API端点：获取目录信息"""
        path_validation = validate_path(subpath)
        if not path_validation or path_validation is False:
            return jsonify({'error': 'Invalid path'}), 403

        _, normalized_path, current_path = path_validation

        if not os.path.exists(current_path) or not os.path.isdir(current_path):
            return jsonify({'error': 'Directory not found'}), 404

        try:
            # 获取目录修改时间用于缓存
            try:
                dir_mtime = os.path.getmtime(current_path)
            except OSError:
                dir_mtime = time.time()

            items = get_directory_items_cached(
                current_path,
                str(app.config['SUPPORTED_EXTENSIONS']),
                dir_mtime
            )

            response = jsonify({
                'path': normalized_path,
                'items': items,
                'total': len(items)
            })

            # 添加缓存头
            response.headers['Cache-Control'] = 'public, max-age=300'
            response.headers['ETag'] = hashlib.md5(f"{current_path}-{dir_mtime}".encode()).hexdigest()

            return response

        except Exception as e:
            logging.error(f"Error in API directory route: {e}")
            return jsonify({'error': 'Internal server error'}), 500
    
    return app

def get_directory_items_uncached(directory_path, supported_extensions):
    """获取目录下的文件和文件夹（无缓存版本）"""
    all_items = []
    try:
        for item in os.listdir(directory_path):
            # 跳过隐藏文件和系统文件
            if item.startswith('.') or item.startswith('$'):
                continue

            item_path = os.path.join(directory_path, item)

            try:
                if os.path.isdir(item_path):
                    # 获取文件夹信息
                    try:
                        item_count = len([f for f in os.listdir(item_path)
                                        if not f.startswith('.') and not f.startswith('$')])
                    except (PermissionError, OSError):
                        item_count = 0

                    all_items.append({
                        'name': item,
                        'type': 'dir',
                        'item_count': item_count
                    })
                elif item.lower().endswith(supported_extensions):
                    # 获取文件信息
                    try:
                        file_size = os.path.getsize(item_path)
                        file_size_str = format_file_size(file_size)
                    except (OSError, IOError):
                        file_size_str = "未知"

                    all_items.append({
                        'name': item,
                        'type': 'file',
                        'size': file_size_str
                    })
            except (OSError, IOError):
                # 跳过无法访问的项目
                continue

    except PermissionError:
        logging.warning(f"Permission denied accessing directory: {directory_path}")
    except Exception as e:
        logging.error(f"Error reading directory {directory_path}: {e}")

    # 排序：文件夹在前，文件在后
    all_items.sort(key=lambda x: (x['type'] != 'dir', x['name'].lower()))
    return all_items

def format_file_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.1f} {size_names[i]}"

def get_directory_items(directory_path, supported_extensions):
    """获取目录项目的包装函数，自动处理缓存"""
    try:
        dir_mtime = os.path.getmtime(directory_path)
    except OSError:
        dir_mtime = time.time()

    return get_directory_items_cached(
        directory_path,
        str(supported_extensions),
        dir_mtime
    )

def paginate_items(items, page, per_page):
    """分页处理"""
    start = (page - 1) * per_page
    end = start + per_page
    paginated_items = items[start:end]
    total_pages = (len(items) + per_page - 1) // per_page
    return paginated_items, total_pages

if __name__ == '__main__':
    app = create_app()
    print(f"服务器启动中...")
    print(f"本地访问: http://localhost:{app.config['PORT']}")
    print(f"局域网访问: http://**********:{app.config['PORT']}")
    print(f"图片目录: {app.config['IMAGE_ROOT']}")
    
    app.run(
        host=app.config['HOST'],
        port=app.config['PORT'],
        debug=app.config['DEBUG']
    )
