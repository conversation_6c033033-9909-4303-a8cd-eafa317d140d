import os
from flask import Flask, render_template, send_from_directory, request, abort
from config import Config

def create_app(config_class=Config):
    """应用工厂函数"""
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    @app.route('/')
    def index():
        """首页，显示根目录下的内容"""
        return gallery('.')

    @app.route('/<path:subpath>')
    def gallery(subpath):
        """显示子目录下的内容"""
        page = request.args.get('page', 1, type=int)
        current_path = os.path.join(app.config['IMAGE_ROOT'], subpath)
        
        # 安全检查：防止路径遍历攻击
        if not os.path.commonpath([app.config['IMAGE_ROOT'], current_path]) == app.config['IMAGE_ROOT']:
            abort(403)
            
        if not os.path.isdir(current_path):
            abort(404)

        all_items = get_directory_items(current_path, app.config['SUPPORTED_EXTENSIONS'])
        
        # 分页处理
        paginated_items, total_pages = paginate_items(all_items, page, app.config['PER_PAGE'])
        
        return render_template('index.html',
                             items=paginated_items,
                             current_dir=subpath,
                             page=page,
                             total_pages=total_pages)

    @app.route('/images/<path:filename>')
    def get_image(filename):
        """提供图片文件"""
        # 安全检查
        file_path = os.path.join(app.config['IMAGE_ROOT'], filename)
        if not os.path.commonpath([app.config['IMAGE_ROOT'], file_path]) == app.config['IMAGE_ROOT']:
            abort(403)
        return send_from_directory(app.config['IMAGE_ROOT'], filename)
    
    return app

def get_directory_items(directory_path, supported_extensions):
    """获取目录下的文件和文件夹"""
    all_items = []
    try:
        for item in os.listdir(directory_path):
            item_path = os.path.join(directory_path, item)
            if os.path.isdir(item_path):
                all_items.append({'name': item, 'type': 'dir'})
            elif item.lower().endswith(supported_extensions):
                all_items.append({'name': item, 'type': 'file'})
    except PermissionError:
        pass
    
    # 排序：文件夹在前，文件在后
    all_items.sort(key=lambda x: (x['type'] != 'dir', x['name'].lower()))
    return all_items

def paginate_items(items, page, per_page):
    """分页处理"""
    start = (page - 1) * per_page
    end = start + per_page
    paginated_items = items[start:end]
    total_pages = (len(items) + per_page - 1) // per_page
    return paginated_items, total_pages

if __name__ == '__main__':
    app = create_app()
    print(f"服务器启动中...")
    print(f"本地访问: http://localhost:{app.config['PORT']}")
    print(f"局域网访问: http://**********:{app.config['PORT']}")
    print(f"图片目录: {app.config['IMAGE_ROOT']}")
    
    app.run(
        host=app.config['HOST'],
        port=app.config['PORT'],
        debug=app.config['DEBUG']
    )
