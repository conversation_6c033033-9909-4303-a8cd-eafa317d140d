import os
import socket
import logging

class Config:
    """基础配置"""
    # 图片存放的根目录
    IMAGE_ROOT = os.path.join(os.path.expanduser('~'), 'Downloads', 'XZ')
    PER_PAGE = 30  # 每页显示项目数

    # 支持的图片格式
    SUPPORTED_EXTENSIONS = ('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp', '.tiff', '.svg')

    # 服务器配置
    HOST = '0.0.0.0'  # 允许局域网访问
    PORT = 5000
    DEBUG = True

    # 安全配置
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB 最大文件大小

    # 缓存配置
    SEND_FILE_MAX_AGE_DEFAULT = 31536000  # 1年缓存

    @staticmethod
    def get_local_ip():
        """获取本机局域网IP地址"""
        try:
            # 创建一个UDP socket连接到外部地址来获取本机IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
                return local_ip
        except Exception:
            try:
                # 备用方法：获取主机名对应的IP
                hostname = socket.gethostname()
                local_ip = socket.gethostbyname(hostname)
                if local_ip.startswith("127."):
                    # 如果是回环地址，尝试其他方法
                    return "localhost"
                return local_ip
            except Exception:
                return "localhost"

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True