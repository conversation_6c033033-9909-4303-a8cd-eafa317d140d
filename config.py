import os

class Config:
    """基础配置"""
    # 图片存放的根目录
    IMAGE_ROOT = os.path.join(os.path.expanduser('~'), 'Downloads', 'XZ')
    PER_PAGE = 30  # 每页显示项目数
    
    # 支持的图片格式
    SUPPORTED_EXTENSIONS = ('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp')
    
    # 服务器配置
    HOST = '0.0.0.0'  # 默认允许局域网访问
    PORT = 5000
    DEBUG = True

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True