import logging
import logging.handlers
import os
from datetime import datetime

def setup_logging(app):
    """配置应用日志"""
    
    # 创建logs目录
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 配置文件处理器（按日期轮转）
    file_handler = logging.handlers.TimedRotatingFileHandler(
        filename=os.path.join(log_dir, 'app.log'),
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.INFO)
    
    # 配置错误文件处理器
    error_handler = logging.handlers.TimedRotatingFileHandler(
        filename=os.path.join(log_dir, 'error.log'),
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    error_handler.setFormatter(formatter)
    error_handler.setLevel(logging.ERROR)
    
    # 配置控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO if app.debug else logging.WARNING)
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(error_handler)
    root_logger.addHandler(console_handler)
    
    # 配置Flask应用日志
    app.logger.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.addHandler(error_handler)
    
    # 配置Werkzeug日志（减少噪音）
    werkzeug_logger = logging.getLogger('werkzeug')
    werkzeug_logger.setLevel(logging.WARNING)
    
    app.logger.info(f"应用启动 - 调试模式: {app.debug}")
    return app.logger

class RequestLogger:
    """请求日志中间件"""
    
    def __init__(self, app):
        self.app = app
        self.logger = logging.getLogger('request')
        
    def __call__(self, environ, start_response):
        start_time = datetime.now()
        
        def new_start_response(status, response_headers):
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds() * 1000
            
            # 记录请求信息
            self.logger.info(
                f"{environ.get('REMOTE_ADDR')} - "
                f"{environ.get('REQUEST_METHOD')} "
                f"{environ.get('PATH_INFO')} - "
                f"状态: {status} - "
                f"耗时: {duration:.2f}ms"
            )
            
            return start_response(status, response_headers)
        
        return self.app(environ, new_start_response)

def log_error(error_type, error_message, request_info=None):
    """记录错误信息"""
    logger = logging.getLogger('error')
    
    error_info = f"错误类型: {error_type} - 错误信息: {error_message}"
    
    if request_info:
        error_info += f" - 请求信息: {request_info}"
    
    logger.error(error_info)

def log_security_event(event_type, details, client_ip=None):
    """记录安全事件"""
    logger = logging.getLogger('security')
    
    security_info = f"安全事件: {event_type} - 详情: {details}"
    
    if client_ip:
        security_info += f" - 客户端IP: {client_ip}"
    
    logger.warning(security_info)
