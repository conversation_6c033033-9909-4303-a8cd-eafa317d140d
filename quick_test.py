#!/usr/bin/env python3
"""
快速测试脚本
"""
import sys
import os

def test_imports():
    """测试导入"""
    print("测试模块导入...")
    
    try:
        from config import Config, DevelopmentConfig, ProductionConfig
        print("✅ config模块导入成功")
    except Exception as e:
        print(f"❌ config模块导入失败: {e}")
        return False
    
    try:
        from app import create_app
        print("✅ app模块导入成功")
    except Exception as e:
        print(f"❌ app模块导入失败: {e}")
        return False
    
    try:
        from logging_config import setup_logging
        print("✅ logging_config模块导入成功")
    except Exception as e:
        print(f"❌ logging_config模块导入失败: {e}")
        return False
    
    return True

def test_app_creation():
    """测试应用创建"""
    print("\n测试应用创建...")
    
    try:
        from app import create_app
        from config import DevelopmentConfig
        
        app = create_app(DevelopmentConfig)
        print("✅ Flask应用创建成功")
        print(f"   HOST: {app.config.get('HOST')}")
        print(f"   PORT: {app.config.get('PORT')}")
        print(f"   DEBUG: {app.config.get('DEBUG')}")
        print(f"   IMAGE_ROOT: {app.config.get('IMAGE_ROOT')}")
        
        return True
    except Exception as e:
        print(f"❌ Flask应用创建失败: {e}")
        return False

def test_config():
    """测试配置"""
    print("\n测试配置...")
    
    try:
        from config import Config
        
        print(f"✅ 配置加载成功")
        print(f"   图片目录: {Config.IMAGE_ROOT}")
        print(f"   每页项目数: {Config.PER_PAGE}")
        print(f"   支持格式: {Config.SUPPORTED_EXTENSIONS}")
        
        # 检查图片目录是否存在
        if os.path.exists(Config.IMAGE_ROOT):
            print(f"✅ 图片目录存在")
        else:
            print(f"⚠️  图片目录不存在: {Config.IMAGE_ROOT}")
            print("   这不会影响应用启动，但需要配置正确的目录")
        
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_network():
    """测试网络配置"""
    print("\n测试网络配置...")
    
    try:
        from config import Config
        
        local_ip = Config.get_local_ip()
        print(f"✅ 本机IP检测成功: {local_ip}")
        
        return True
    except Exception as e:
        print(f"❌ 网络配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("Flask图片浏览器 - 快速测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("配置检查", test_config),
        ("网络配置", test_network),
        ("应用创建", test_app_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    
    if passed == total:
        print("🎉 所有测试通过！应用已准备就绪。")
        print("\n启动应用:")
        print("  python run.py")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
