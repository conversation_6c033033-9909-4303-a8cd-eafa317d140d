#!/usr/bin/env python3
"""
启动脚本
"""
from app import create_app
from config import Config, DevelopmentConfig, ProductionConfig
import os
import logging

def main():
    # 根据环境变量选择配置
    env = os.environ.get('FLASK_ENV', 'development')

    if env == 'production':
        config = ProductionConfig
    else:
        config = DevelopmentConfig

    app = create_app(config)

    # 获取本机IP地址
    local_ip = config.get_local_ip()

    print(f"=== 图片浏览器启动 ===")
    print(f"环境: {env}")
    print(f"本地访问: http://localhost:{config.PORT}")
    print(f"局域网访问: http://{local_ip}:{config.PORT}")
    print(f"图片目录: {config.IMAGE_ROOT}")
    print(f"每页显示: {config.PER_PAGE} 个项目")
    print(f"支持格式: {', '.join(config.SUPPORTED_EXTENSIONS)}")

    # 检查图片目录是否存在
    if not os.path.exists(config.IMAGE_ROOT):
        print(f"⚠️  警告: 图片目录不存在: {config.IMAGE_ROOT}")
        print("   请确保目录存在或修改config.py中的IMAGE_ROOT配置")
    else:
        print(f"✅ 图片目录已就绪")

    print("=" * 50)
    print("🌐 局域网访问信息:")
    print(f"   主要地址: http://{local_ip}:{config.PORT}")
    print(f"   备用地址: http://localhost:{config.PORT}")
    print()
    print("📱 移动设备访问:")
    print(f"   在手机/平板浏览器中输入: http://{local_ip}:{config.PORT}")
    print()
    print("🔧 故障排除:")
    print("   1. 确保所有设备连接到同一WiFi网络")
    print("   2. 检查防火墙是否允许端口访问")
    print("   3. 某些路由器可能需要启用AP隔离功能")
    print(f"   4. 运行 'python test_lan.py' 进行网络测试")
    print("=" * 50)

    try:
        app.run(
            host=config.HOST,
            port=config.PORT,
            debug=config.DEBUG,
            threaded=True  # 启用多线程支持
        )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        logging.error(f"Server startup failed: {e}")

if __name__ == '__main__':
    main()