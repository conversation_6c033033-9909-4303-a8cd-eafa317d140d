#!/usr/bin/env python3
"""
启动脚本
"""
from app import create_app
from config import Config, DevelopmentConfig, ProductionConfig
import os

def main():
    # 根据环境变量选择配置
    env = os.environ.get('FLASK_ENV', 'development')
    
    if env == 'production':
        config = ProductionConfig
    else:
        config = DevelopmentConfig
    
    app = create_app(config)
    
    print(f"=== 图片浏览器启动 ===")
    print(f"环境: {env}")
    print(f"本地访问: http://localhost:{config.PORT}")
    print(f"局域网访问: http://**********:{config.PORT}")
    print(f"图片目录: {config.IMAGE_ROOT}")
    print(f"每页显示: {config.PER_PAGE} 个项目")
    print("=" * 30)
    
    app.run(
        host=config.HOST,
        port=config.PORT,
        debug=config.DEBUG
    )

if __name__ == '__main__':
    main()