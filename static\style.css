/* 全局样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    margin-top: 20px;
    margin-bottom: 20px;
    min-height: calc(100vh - 40px);
}

/* 头部样式 */
.header {
    border-bottom: 2px solid #f0f2f5;
    padding-bottom: 20px;
    margin-bottom: 20px;
}

.header-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.title {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.title .icon {
    font-size: 32px;
}

/* 面包屑导航 */
.breadcrumb {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
    font-size: 14px;
}

.breadcrumb-item {
    color: #007bff;
    text-decoration: none;
    padding: 6px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.breadcrumb-item:hover {
    background-color: #f8f9fa;
    text-decoration: none;
}

.breadcrumb-separator {
    color: #6c757d;
    font-weight: bold;
}

/* 工具栏样式 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.toolbar-left, .toolbar-center, .toolbar-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.toolbar-center {
    flex: 1;
    justify-content: center;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    text-decoration: none;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
}

.btn-back {
    background-color: #6c757d;
    color: white;
}

.btn-back:hover {
    background-color: #545b62;
    text-decoration: none;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

/* 搜索框样式 */
.search-container {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 25px;
    padding: 2px;
    min-width: 300px;
}

.search-input {
    border: none;
    background: transparent;
    padding: 10px 15px;
    font-size: 14px;
    flex: 1;
    outline: none;
}

.search-btn {
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-btn:hover {
    background-color: #0056b3;
}

/* 视图控制 */
.view-controls {
    display: flex;
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 2px;
}

.view-btn {
    background: transparent;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 16px;
}

.view-btn.active {
    background-color: #007bff;
    color: white;
}

.info-display {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

/* 主内容区域 */
.main-content {
    min-height: 400px;
}

/* 画廊样式 */
.gallery {
    display: grid;
    gap: 20px;
    margin-bottom: 30px;
}

.gallery.grid-view {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.gallery.list-view {
    grid-template-columns: 1fr;
}

/* 项目样式 */
.item {
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.item-link {
    text-decoration: none;
    color: inherit;
    display: block;
    height: 100%;
}

/* 文件夹项目 */
.dir-item .item-preview {
    padding: 30px 20px;
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
}

.dir-item .icon {
    font-size: 48px;
    margin-bottom: 10px;
    display: block;
}

.item-count {
    font-size: 12px;
    opacity: 0.9;
}

/* 图片项目 */
.image-item .item-preview {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.image-item:hover .thumbnail {
    transform: scale(1.05);
}

.item-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-item:hover .item-overlay {
    opacity: 1;
}

.preview-btn, .download-btn {
    background-color: rgba(255,255,255,0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
}

.preview-btn:hover, .download-btn:hover {
    background-color: white;
    transform: scale(1.1);
}

/* 项目信息 */
.item-info {
    padding: 15px;
    border-top: 1px solid #f0f2f5;
}

.item-name {
    display: block;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #2c3e50;
}

.item-type, .item-size {
    font-size: 12px;
    color: #6c757d;
}

/* 列表视图样式 */
.list-view .item {
    display: flex;
    align-items: center;
    padding: 15px;
}

.list-view .item-preview {
    width: 60px;
    height: 60px;
    margin-right: 15px;
    flex-shrink: 0;
}

.list-view .dir-item .item-preview {
    padding: 10px;
    border-radius: 8px;
}

.list-view .image-item .item-preview {
    height: 60px;
    border-radius: 8px;
}

.list-view .item-info {
    flex: 1;
    padding: 0;
    border: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.list-view .item-name {
    margin-bottom: 0;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-icon {
    font-size: 80px;
    margin-bottom: 20px;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #495057;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 40px;
    padding: 20px 0;
    border-top: 1px solid #e9ecef;
    flex-wrap: wrap;
    gap: 15px;
}

.pagination-info {
    font-size: 14px;
    color: #6c757d;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 5px;
}

.page-btn {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background-color: white;
    color: #007bff;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 14px;
    min-width: 40px;
    text-align: center;
}

.page-btn:hover {
    background-color: #e9ecef;
    text-decoration: none;
}

.page-btn.current {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.8);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    animation: slideIn 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background-color: #f8f9fa;
    color: #495057;
}

.modal-body {
    padding: 0;
    text-align: center;
    max-height: 60vh;
    overflow: auto;
}

.modal-image {
    max-width: 100%;
    max-height: 60vh;
    object-fit: contain;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 加载指示器 */
.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0,0,0,0.8);
    color: white;
    padding: 20px;
    border-radius: 8px;
    display: none;
    z-index: 1001;
    text-align: center;
}

.spinner {
    border: 3px solid rgba(255,255,255,0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

/* 动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 15px;
        border-radius: 8px;
    }

    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }

    .toolbar-left, .toolbar-center, .toolbar-right {
        justify-content: center;
    }

    .search-container {
        min-width: auto;
        width: 100%;
    }

    .gallery.grid-view {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .pagination {
        flex-direction: column;
        text-align: center;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .breadcrumb {
        font-size: 12px;
    }

    .title {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .gallery.grid-view {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 15px;
    }

    .item-preview {
        height: 150px !important;
    }

    .pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
}