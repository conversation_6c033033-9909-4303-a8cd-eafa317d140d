<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误 {{ error_code }} - 图片浏览器</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <style>
        .error-container {
            text-align: center;
            padding: 50px 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .error-code {
            font-size: 72px;
            font-weight: bold;
            color: #dc3545;
            margin-bottom: 20px;
        }
        
        .error-message {
            font-size: 24px;
            color: #6c757d;
            margin-bottom: 30px;
        }
        
        .error-description {
            font-size: 16px;
            color: #868e96;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .error-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .error-icon {
            font-size: 100px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-container">
            <div class="error-icon">
                {% if error_code == 404 %}
                    🔍
                {% elif error_code == 403 %}
                    🚫
                {% elif error_code == 500 %}
                    ⚠️
                {% elif error_code == 413 %}
                    📁
                {% else %}
                    ❌
                {% endif %}
            </div>
            
            <div class="error-code">{{ error_code }}</div>
            <div class="error-message">{{ error_message }}</div>
            
            <div class="error-description">
                {% if error_code == 404 %}
                    您访问的页面或文件不存在，可能已被移动或删除。
                {% elif error_code == 403 %}
                    您没有权限访问此资源，或路径包含不安全的字符。
                {% elif error_code == 500 %}
                    服务器遇到了一个错误，请稍后再试。
                {% elif error_code == 413 %}
                    上传的文件太大，请选择较小的文件。
                {% else %}
                    发生了未知错误，请联系管理员。
                {% endif %}
            </div>
            
            <div class="error-actions">
                <a href="{{ url_for('index') }}" class="btn btn-primary">返回首页</a>
                <a href="javascript:history.back()" class="btn btn-secondary">返回上页</a>
            </div>
        </div>
    </div>
</body>
</html>
