<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片浏览器 - {{ current_dir if current_dir != '.' else '根目录' }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📁</text></svg>">
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="header-content">
                <h1 class="title">
                    <span class="icon">📁</span>
                    图片浏览器
                </h1>

                <!-- 面包屑导航 -->
                <nav class="breadcrumb">
                    <a href="{{ url_for('index') }}" class="breadcrumb-item">
                        <span class="icon">🏠</span>
                        根目录
                    </a>
                    {% if current_dir != '.' %}
                        {% set path_parts = current_dir.split('/') %}
                        {% for i in range(path_parts|length) %}
                            {% set partial_path = path_parts[:i+1]|join('/') %}
                            <span class="breadcrumb-separator">></span>
                            <a href="{{ url_for('gallery', subpath=partial_path) }}" class="breadcrumb-item">
                                {{ path_parts[i] }}
                            </a>
                        {% endfor %}
                    {% endif %}
                </nav>
            </div>
        </header>

        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                {% if current_dir != '.' %}
                    <a href="{{ url_for('gallery', subpath=current_dir.rsplit('/', 1)[0] if '/' in current_dir else '.') }}" class="btn btn-back">
                        <span class="icon">⬅️</span>
                        返回上级
                    </a>
                {% endif %}
            </div>

            <div class="toolbar-center">
                <div class="search-container">
                    <input type="text" id="searchInput" placeholder="搜索文件和文件夹..." class="search-input">
                    <button id="searchBtn" class="search-btn">🔍</button>
                </div>
            </div>

            <div class="toolbar-right">
                <div class="view-controls">
                    <button id="gridViewBtn" class="view-btn active" title="网格视图">⊞</button>
                    <button id="listViewBtn" class="view-btn" title="列表视图">☰</button>
                </div>
                <div class="info-display">
                    共 {{ total_items or 0 }} 项
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <main class="main-content">
            {% if items %}
                <div class="gallery" id="gallery">
                    {% for item in items %}
                        {% if item.type == 'dir' %}
                            <div class="item dir-item" data-name="{{ item.name|lower }}" data-type="dir">
                                <a href="{{ url_for('gallery', subpath=(current_dir + '/' + item.name) if current_dir != '.' else item.name) }}" class="item-link">
                                    <div class="item-preview">
                                        <div class="icon">📁</div>
                                        <div class="item-count">{{ item.item_count }} 项</div>
                                    </div>
                                    <div class="item-info">
                                        <span class="item-name" title="{{ item.name }}">{{ item.name }}</span>
                                        <span class="item-type">文件夹</span>
                                    </div>
                                </a>
                            </div>
                        {% else %}
                            <div class="item image-item" data-name="{{ item.name|lower }}" data-type="file">
                                <div class="item-link">
                                    <div class="item-preview">
                                        <img src="{{ url_for('get_image', filename=(current_dir + '/' + item.name) if current_dir != '.' else item.name) }}"
                                             alt="{{ item.name }}"
                                             class="thumbnail"
                                             loading="lazy"
                                             onclick="openImageModal(this)">
                                        <div class="item-overlay">
                                            <button class="preview-btn" onclick="openImageModal(event.target.previousElementSibling)" title="预览">👁️</button>
                                            <a href="{{ url_for('get_image', filename=(current_dir + '/' + item.name) if current_dir != '.' else item.name) }}"
                                               target="_blank"
                                               class="download-btn"
                                               title="查看原图">🔗</a>
                                        </div>
                                    </div>
                                    <div class="item-info">
                                        <span class="item-name" title="{{ item.name }}">{{ item.name }}</span>
                                        <span class="item-size">{{ item.size }}</span>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">📂</div>
                    <h3>此目录为空</h3>
                    <p>当前目录中没有图片文件或子文件夹</p>
                </div>
            {% endif %}
        </main>

        <!-- 分页导航 -->
        {% if total_pages > 1 %}
            <nav class="pagination">
                <div class="pagination-info">
                    第 {{ page }} 页 / 共 {{ total_pages }} 页
                </div>

                <div class="pagination-controls">
                    {% if page > 1 %}
                        <a href="{{ url_for('gallery', subpath=current_dir, page=1) }}" class="page-btn" title="首页">⏮️</a>
                        <a href="{{ url_for('gallery', subpath=current_dir, page=page-1) }}" class="page-btn" title="上一页">⬅️</a>
                    {% endif %}

                    {% set start_page = [1, page - 2]|max %}
                    {% set end_page = [total_pages, page + 2]|min %}

                    {% for p in range(start_page, end_page + 1) %}
                        {% if p == page %}
                            <span class="page-btn current">{{ p }}</span>
                        {% else %}
                            <a href="{{ url_for('gallery', subpath=current_dir, page=p) }}" class="page-btn">{{ p }}</a>
                        {% endif %}
                    {% endfor %}

                    {% if page < total_pages %}
                        <a href="{{ url_for('gallery', subpath=current_dir, page=page+1) }}" class="page-btn" title="下一页">➡️</a>
                        <a href="{{ url_for('gallery', subpath=current_dir, page=total_pages) }}" class="page-btn" title="末页">⏭️</a>
                    {% endif %}
                </div>
            </nav>
        {% endif %}
    </div>

    <!-- 图片预览模态框 -->
    <div id="imageModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="modal-title" id="modalTitle">图片预览</span>
                <button class="modal-close" onclick="closeImageModal()">&times;</button>
            </div>
            <div class="modal-body">
                <img id="modalImage" src="" alt="" class="modal-image">
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeImageModal()">关闭</button>
                <a id="modalDownload" href="" target="_blank" class="btn btn-primary">查看原图</a>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loadingIndicator" class="loading-indicator">
        <div class="spinner"></div>
        <span>加载中...</span>
    </div>

    <script>
        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const items = document.querySelectorAll('.item');

            items.forEach(item => {
                const name = item.dataset.name;
                if (name.includes(searchTerm)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // 视图切换
        document.getElementById('gridViewBtn').addEventListener('click', function() {
            document.getElementById('gallery').className = 'gallery grid-view';
            this.classList.add('active');
            document.getElementById('listViewBtn').classList.remove('active');
        });

        document.getElementById('listViewBtn').addEventListener('click', function() {
            document.getElementById('gallery').className = 'gallery list-view';
            this.classList.add('active');
            document.getElementById('gridViewBtn').classList.remove('active');
        });

        // 图片模态框
        function openImageModal(img) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            const modalTitle = document.getElementById('modalTitle');
            const modalDownload = document.getElementById('modalDownload');

            modal.style.display = 'block';
            modalImg.src = img.src;
            modalTitle.textContent = img.alt;
            modalDownload.href = img.src;

            document.body.style.overflow = 'hidden';
        }

        function closeImageModal() {
            document.getElementById('imageModal').style.display = 'none';
            document.body.style.overflow = '';
        }

        // 点击模态框外部关闭
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImageModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageModal();
            }
        });

        // 图片懒加载错误处理
        document.querySelectorAll('.thumbnail').forEach(img => {
            img.addEventListener('error', function() {
                this.src = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y=".9em" font-size="90">🖼️</text></svg>';
                this.alt = '图片加载失败';
            });
        });
    </script>
</body>
</html>