<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片浏览器 - {{ current_dir }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <h1>当前目录: {{ current_dir }}</h1>
        
        {% if current_dir != '.' %}
            <a href="{{ url_for('gallery', subpath=current_dir.rsplit('/', 1)[0] if '/' in current_dir else '.') }}" class="back-link">返回上一级</a>
        {% endif %}

        <div class="gallery">
            {% for item in items %}
                {% if item.type == 'dir' %}
                    <div class="item dir-item">
                        <a href="{{ url_for('gallery', subpath=(current_dir + '/' + item.name) if current_dir != '.' else item.name) }}">
                            <div class="icon">📁</div>
                            <span>{{ item.name }}</span>
                        </a>
                    </div>
                {% else %}
                    <div class="item image-item">
                        <a href="{{ url_for('get_image', filename=(current_dir + '/' + item.name) if current_dir != '.' else item.name) }}" target="_blank">
                            <img src="{{ url_for('get_image', filename=(current_dir + '/' + item.name) if current_dir != '.' else item.name) }}" alt="{{ item.name }}">
                            <span>{{ item.name }}</span>
                        </a>
                    </div>
                {% endif %}
            {% endfor %}
        </div>

        <div class="pagination">
            {% if page > 1 %}
                <a href="{{ url_for('gallery', subpath=current_dir, page=page-1) }}">&laquo; 上一页</a>
            {% endif %}
            
            <span>第 {{ page }} 页 / 共 {{ total_pages }} 页</span>
            
            {% if page < total_pages %}
                <a href="{{ url_for('gallery', subpath=current_dir, page=page+1) }}">下一页 &raquo;</a>
            {% endif %}
        </div>
    </div>
</body>
</html>