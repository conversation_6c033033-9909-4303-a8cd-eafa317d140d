#!/usr/bin/env python3
"""
LAN功能测试脚本
"""
import requests
import socket
import time
import sys
from config import Config

def get_local_ip():
    """获取本机IP地址"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except Exception:
        return "localhost"

def test_server_accessibility(host, port):
    """测试服务器可访问性"""
    print(f"测试服务器可访问性: {host}:{port}")
    
    try:
        # 测试TCP连接
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(5)
            result = s.connect_ex((host, port))
            if result == 0:
                print("✅ TCP连接成功")
                return True
            else:
                print("❌ TCP连接失败")
                return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def test_http_response(base_url):
    """测试HTTP响应"""
    print(f"测试HTTP响应: {base_url}")
    
    try:
        # 测试首页
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            print("✅ 首页访问成功")
            print(f"   响应时间: {response.elapsed.total_seconds():.2f}秒")
            print(f"   内容长度: {len(response.content)} 字节")
        else:
            print(f"❌ 首页访问失败，状态码: {response.status_code}")
            return False
            
        # 测试API端点
        api_url = f"{base_url}/api/directory/."
        response = requests.get(api_url, timeout=10)
        if response.status_code == 200:
            print("✅ API端点访问成功")
            data = response.json()
            print(f"   返回项目数: {data.get('total', 0)}")
        else:
            print(f"❌ API端点访问失败，状态码: {response.status_code}")
            
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接被拒绝")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ HTTP测试失败: {e}")
        return False

def test_network_interfaces():
    """测试网络接口"""
    print("检查网络接口...")
    
    # 获取所有网络接口
    hostname = socket.gethostname()
    print(f"主机名: {hostname}")
    
    try:
        # 获取主机的所有IP地址
        host_info = socket.gethostbyname_ex(hostname)
        print(f"主机信息: {host_info}")
        
        for ip in host_info[2]:
            if not ip.startswith("127."):
                print(f"发现网络接口: {ip}")
                
    except Exception as e:
        print(f"获取网络接口失败: {e}")

def check_firewall_ports(port):
    """检查防火墙端口"""
    print(f"检查端口 {port} 是否被防火墙阻止...")
    
    # 尝试绑定端口
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('0.0.0.0', port))
            print(f"✅ 端口 {port} 可用")
            return True
    except OSError as e:
        if e.errno == 98:  # Address already in use
            print(f"✅ 端口 {port} 已被使用（这是正常的，如果服务器正在运行）")
            return True
        else:
            print(f"❌ 端口 {port} 不可用: {e}")
            return False

def generate_test_urls():
    """生成测试URL"""
    local_ip = get_local_ip()
    port = Config.PORT
    
    urls = [
        f"http://localhost:{port}",
        f"http://127.0.0.1:{port}",
        f"http://{local_ip}:{port}",
    ]
    
    return urls, local_ip

def main():
    """主测试函数"""
    print("=" * 50)
    print("Flask图片浏览器 LAN功能测试")
    print("=" * 50)
    
    # 获取配置
    port = Config.PORT
    
    # 测试网络接口
    test_network_interfaces()
    print()
    
    # 检查端口
    check_firewall_ports(port)
    print()
    
    # 生成测试URL
    test_urls, local_ip = generate_test_urls()
    
    print("测试URL列表:")
    for url in test_urls:
        print(f"  - {url}")
    print()
    
    # 测试每个URL
    success_count = 0
    for url in test_urls:
        print(f"测试: {url}")
        
        # 解析主机和端口
        if "localhost" in url or "127.0.0.1" in url:
            host = "127.0.0.1"
        else:
            host = local_ip
            
        # 测试连接
        if test_server_accessibility(host, port):
            if test_http_response(url):
                success_count += 1
                print("✅ 测试通过")
            else:
                print("❌ 测试失败")
        else:
            print("❌ 测试失败")
        
        print("-" * 30)
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"总测试数: {len(test_urls)}")
    print(f"成功数: {success_count}")
    print(f"失败数: {len(test_urls) - success_count}")
    
    if success_count == len(test_urls):
        print("🎉 所有测试通过！LAN功能正常工作。")
    elif success_count > 0:
        print("⚠️  部分测试通过。请检查网络配置。")
    else:
        print("❌ 所有测试失败。请检查服务器是否运行和网络配置。")
    
    print("\n局域网访问说明:")
    print(f"1. 确保Flask服务器正在运行")
    print(f"2. 局域网内其他设备可通过以下地址访问:")
    print(f"   http://{local_ip}:{port}")
    print(f"3. 确保防火墙允许端口 {port} 的入站连接")
    print(f"4. 确保所有设备都在同一个局域网内")

if __name__ == "__main__":
    main()
